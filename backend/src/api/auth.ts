import { Router, Request, Response } from 'express';
import { GoogleAuthService } from '../auth/googleAuth.js';
import { UserModel } from '../database/models/User.js';
import { authMiddleware } from '../middleware/auth.js';
import * as SharedTypes from '../../../shared/types';
const { ValidationError } = SharedTypes;
const { AuthError } = SharedTypes;
// import { ValidationError } from '../../../shared/types.js';
import type { ApiResponse, User, AuthTokens } from '../../../shared/types.js';

const router = Router();
const googleAuth = new GoogleAuthService();

/**
 * GET /auth/google
 * Redirect to Google OAuth
 */
router.get('/google', (req: Request, res: Response) => {
  try {
    const authUrl = googleAuth.getAuthUrl();
    res.redirect(authUrl);
  } catch (error) {
    console.error('Google auth URL generation error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'AUTH_URL_ERROR',
        message: 'Failed to generate authentication URL'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /auth/google/callback
 * Handle Google OAuth callback
 */
router.get('/google/callback', async (req: Request, res: Response) => {
  try {
    const { code, error } = req.query;

    if (error) {
      console.error('Google OAuth error:', error);
      return res.redirect(`${process.env.FRONTEND_URL}/auth/error?error=${encodeURIComponent(error as string)}`);
    }

    if (!code || typeof code !== 'string') {
      return res.redirect(`${process.env.FRONTEND_URL}/auth/error?error=missing_code`);
    }

    const { user, tokens } = await googleAuth.handleCallback(code);

    // In production, you might want to set secure HTTP-only cookies
    // For now, we'll redirect with tokens in URL (not recommended for production)
    const redirectUrl = new URL(`${process.env.FRONTEND_URL}/auth/success`);
    redirectUrl.searchParams.set('token', tokens.accessToken);
    redirectUrl.searchParams.set('refresh_token', tokens.refreshToken || '');
    redirectUrl.searchParams.set('user', JSON.stringify({
      id: user.id,
      name: user.name,
      email: user.email
    }));

    res.redirect(redirectUrl.toString());

  } catch (error) {
    console.error('Google OAuth callback error:', error);
    
    const errorMessage = error instanceof AuthError 
      ? (error as Error).message
      : 'Authentication failed';
    
    res.redirect(`${process.env.FRONTEND_URL}/auth/error?error=${encodeURIComponent(errorMessage)}`);
  }
});

/**
 * OPTIONS /auth/google/verify
 * Handle preflight request for CORS
 */
router.options('/google/verify', (req: Request, res: Response) => {
  res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.status(200).end();
});

/**
 * POST /auth/google/verify
 * Verify Google ID token (for frontend direct authentication)
 */
router.post('/google/verify', async (req: Request, res: Response) => {
  try {
    // Set CORS headers explicitly for this endpoint
    res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

    const { idToken } = req.body;

    if (!idToken) {
      throw new ValidationError('ID token is required');
    }

    const user = await googleAuth.verifyIdToken(idToken);
    const tokens = googleAuth.generateTokens(user);

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          profileData: user.profileData
        },
        tokens
      },
      timestamp: new Date()
    } as ApiResponse<{ user: Partial<User>; tokens: AuthTokens }>);

  } catch (error) {
    console.error('Google ID token verification error:', error);
    
    if (error instanceof AuthError || error instanceof ValidationError) {
      res.status(400).json({
        success: false,
        error: {
          code: (error as any).code || 'AUTH_ERROR',
          message: (error as Error).message
        },
        timestamp: new Date()
      } as ApiResponse);
    } else {
      res.status(500).json({
        success: false,
        error: {
          code: 'VERIFICATION_ERROR',
          message: 'Failed to verify Google ID token'
        },
        timestamp: new Date()
      } as ApiResponse);
    }
  }
});

/**
 * POST /auth/refresh
 * Refresh access token using refresh token
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new ValidationError('Refresh token is required');
    }

    const tokens = await googleAuth.refreshToken(refreshToken);

    res.json({
      success: true,
      data: { tokens },
      timestamp: new Date()
    } as ApiResponse<{ tokens: AuthTokens }>);

  } catch (error) {
    console.error('Token refresh error:', error);
    
    if (error instanceof AuthError || error instanceof ValidationError) {
      res.status(401).json({
        success: false,
        error: {
          code: (error as any).code || 'AUTH_ERROR',
          message: (error as Error).message
        },
        timestamp: new Date()
      } as ApiResponse);
    } else {
      res.status(500).json({
        success: false,
        error: {
          code: 'REFRESH_ERROR',
          message: 'Failed to refresh token'
        },
        timestamp: new Date()
      } as ApiResponse);
    }
  }
});

/**
 * GET /auth/me
 * Get current user information
 */
router.get('/me', authMiddleware.authenticate, async (req: Request, res: Response) => {
  try {
    const user = req.user!;
    
    // Get user stats
    const stats = await UserModel.getUserStats(user.id);

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          profileData: user.profileData,
          createdAt: user.createdAt
        },
        stats
      },
      timestamp: new Date()
    } as ApiResponse<{ user: Partial<User>; stats: any }>);

  } catch (error) {
    console.error('Get user info error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'USER_INFO_ERROR',
        message: 'Failed to get user information'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * PUT /auth/profile
 * Update user profile
 */
router.put('/profile', authMiddleware.authenticate, async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const { name, profileData } = req.body;

    const updatedUser = await UserModel.update(userId, {
      name,
      profileData
    });

    if (!updatedUser) {
      throw new Error('Failed to update user profile');
    }

    res.json({
      success: true,
      data: {
        user: {
          id: updatedUser.id,
          name: updatedUser.name,
          email: updatedUser.email,
          profileData: updatedUser.profileData,
          updatedAt: updatedUser.updatedAt
        }
      },
      timestamp: new Date()
    } as ApiResponse<{ user: Partial<User> }>);

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PROFILE_UPDATE_ERROR',
        message: 'Failed to update user profile'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * POST /auth/logout
 * Logout user (invalidate token)
 */
router.post('/logout', authMiddleware.authenticate, async (req: Request, res: Response) => {
  try {
    // In a real implementation, you might want to blacklist the token
    // For now, we'll just return success
    res.json({
      success: true,
      data: { message: 'Logged out successfully' },
      timestamp: new Date()
    } as ApiResponse<{ message: string }>);

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'LOGOUT_ERROR',
        message: 'Failed to logout'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

export default router;
